import Head from 'next/head'
import MainFrame from '../../components/CardGenerator/Main'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import { APP_TYPE } from '../../utils/constants'
import { useTranslation } from 'next-i18next'

export default function Home() {
  const { t } = useTranslation('lesson-plans')

  return (
    <>
      <MainFrame app={APP_TYPE.lessonPlanner} />
    </>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['lesson-plans', 'common'])),
    },
  }
}
