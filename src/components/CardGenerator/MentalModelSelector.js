import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Input, Select } from '@chakra-ui/react'
import { useTranslation } from 'next-i18next'
import { useEffect, useMemo, useState } from 'react';
import { APP_TYPE } from '../../utils/constants';

export const Mental_Models = {
  decision: [{
    id: 'auto'
  }, {
    id: 'pros_cons'
  }, {
    id: 'swot_analysis'
  }, {
    id: 'first_principle'
  }, {
    id: 'six_thinking_hats'
  }, {
    id: 'cost_benefit_analysis'
  }, {
    id: 'decision_tree'
  }, {
    id: 'decision_matrix'
  }, {
    id: 'casual_chain'
  }, {
    id: 'systems_thinking'
  }, {
    id: 'second_order_thinking'
  }, {
    id: 'inversion_thinking'
  }, {
    id: 'rephrazing'
  }, {
    id: 'scientific_method'
  }, {
    id: 'changing_perspectives'
  }, {
    id: 'reverse_thinking'
  }, {
    id: 'other'
  }],

  brainstorming: [{
    id: 'auto'
  }, {
    id: 'swot_analysis'
  }, {
    id: 'business_model_canvas'
  }, {
    id: 'eisenhower_matrix'
  }, {
    id: 'first_principle'
  }, {
    id: 'fivew1h_method'
  }, {
    id: 'scamper_method'
  }, {
    id: 'six_thinking_hats'
  }, {
    id: 'pyramid_principle'
  }, {
    id: 'systems_thinking'
  }, {
    id: 'dialectical_thinking'
  }, {
    id: 'probabilistic_thinking',
  }, {
    id: 'steep_analysis'
  }, {
    id: 'five_forces'
  }, {
    id: 'four_p'
  }, {
    id: 'triz'
  }, {
    id: 'rephrazing'
  }, {
    id: 'scientific_method'
  }, {
    id: 'learning_pyramid',
  }, {
    id: 'occams_razor'
  }, {
    id: 'changing_perspectives'
  }, {
    id: 'reverse_thinking'
  }, {
    id: 'role_playing'
  }, {
    id: 'mece_principle'
  }, {
    id: 'value_proposition_canvas'
  }, {
    id: 'other'
  }],

  "lesson-plans": [{
    id: 'bloom'
  }, {
    id: 'marzano'
  }, {
    id: '5e'
  }, {
    id: 'addie'
  }, {
    id: 'gagne'
  }, {
    id: 'constructivist'
  }, {
    id: 'other'
  }]
}

const MentalModelSelector = ({ app, onSelect, value, onInput }) => {
  const { t } = useTranslation('common');

  const [mentalModels, setMentalModels] = useState();
  const isMentalModelSpecificApp = useMemo(() => [APP_TYPE.mindkit, APP_TYPE.mindsnap].includes(app));

  useEffect(() => {
    setMentalModels(Mental_Models[isMentalModelSpecificApp && 'brainstorming' || app.toLowerCase()]?.
      filter(mm => isMentalModelSpecificApp || mm.id !== 'auto').
      map(mm => {
        return {
          value: mm.id,
          label: t(mm.id)
        }
      }))
  }, [app, t])

  return (
    <HStack width={'100%'}>
      <FormLabel whiteSpace="nowrap" m={0} marginRight={0}>{t(APP_TYPE.lessonPlanner === app && 'educational_model' || 'mental_model')}</FormLabel>
      <Select
        bg='white'
        variant={'ghost'}
        onChange={(e) => {
          const selectedValue = e.target.value;
          onSelect(selectedValue ? mentalModels.find(type => type.value === selectedValue) : null);
        }}
        value={value?.value || ''}
        isRequired={app === APP_TYPE.mindkit}
      >
        {
          !isMentalModelSpecificApp &&
          <option value="">{t('select_none')}</option>
        }
        {mentalModels?.map((type) => (
          <option key={type.value} value={type.value}>
            {type.label}
          </option>
        ))}
      </Select>
      {
        value?.value === 'other' &&
        <Input
          value={value.inputValue}
          onChange={onInput}
          placeholder={t('enter_mental_model')}
          border="none"
          _focus={{ boxShadow: 'none' }}
          zIndex="1"
          autoFocus={true}
          backgroundColor={'white'}
        />
      }
    </HStack>
  )
}

export default MentalModelSelector
