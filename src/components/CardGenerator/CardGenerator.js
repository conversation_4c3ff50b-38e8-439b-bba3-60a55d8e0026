import { useEffect, useMemo, useState } from 'react';
import { Button, VStack, useToast, HStack, Link, Flex, Text, useBreakpointValue, Box, ButtonGroup, Tabs, TabList, Tab } from '@chakra-ui/react';
import InputForm from './InputForm';
import CardTypeSelector from './CardTypeSelector';
import LanguageSelector from './LanguageSelector';
import ExampleButtons from './ExampleButtons';
import { useAuth } from '../../contexts/AuthContext';
import { apiUrl } from '../../utils/apiUtils';
import { CloseIcon } from '@chakra-ui/icons';
import { cardTypes, infographicPromptId, onePageSlidesPromptId } from '../../config/cardTypes';
import { useTranslation } from 'next-i18next';
import ArtifactCard from './ArtifactCard';
import { fetchUrlContent } from '../../utils/urlUtils';
import { isValidURL } from '../../utils/urlUtils';
import { APP_TYPE } from '../../utils/constants';
import MentalModelSelector, { Mental_Models } from './MentalModelSelector';
import PersonalForm from './PersonalForm';
// import { horoscopeCalculator } from '../../utils/astrology_calculator';
import horoscopeCalculator from '../../utils/fortuneCalculator';
import HoroscopeRange from './HoroscopeRange';
import ImageStyleSelector from './ImageStyleSelector';

const CardGenerator = ({ onLoginRequired, showExample = false, app, mental_model }) => {
    const { t } = useTranslation('common');
    const isMobile = useBreakpointValue({ base: true, md: false })
    const [mode, setMode] = useState();
    const [cardType, setCardType] = useState();
    const [inputData, setInputData] = useState({});
    const [aiGenerated, setAiGenerated] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedLanguage, setSelectedLanguage] = useState('English');
    const [selectedImageStyle, setSelectedImageStyle] = useState();
    const toast = useToast();
    const { isLoggedIn } = useAuth();
    const [selectableCardTypes, setSelectableCardTypes] = useState([]);
    const [inputFocus, setInputFocus] = useState();
    const [mentalModel, setMentalModel] = useState(app === APP_TYPE.mindkit && { value: 'first_principle', label: 'First Principles Thinking' });
    const [personalInfo, setPersonalInfo] = useState({ gender: 'female' });
    const [horoscopeInfo, setHoroscopeInfo] = useState();

    useEffect(() => {
        cardTypes && cardTypes[mode] && setSelectableCardTypes(cardTypes[mode]);
    }, [mode]);

    useEffect(() => {
        setMode([APP_TYPE.mindmap, APP_TYPE.reading].includes(app) && 'book' || app === APP_TYPE.movie && 'movie' || [APP_TYPE.art, APP_TYPE.photo, APP_TYPE.erase, APP_TYPE.avatar].includes(app) && 'image' || [APP_TYPE.criticalThinking, APP_TYPE.reflection, APP_TYPE.bias, APP_TYPE.dreamlens, APP_TYPE.counselor, APP_TYPE.businessmodel, APP_TYPE.startupmentor, APP_TYPE.okr].includes(app) && 'others' || app === APP_TYPE.slides && 'topic' || [APP_TYPE.onePageSlides, APP_TYPE.infographic].includes(app) && 'makeGraph' || app === APP_TYPE.youtube && 'video' || app === APP_TYPE.poetic && 'imageInsights' || app === APP_TYPE.insightcards && 'cardGuru' || app === APP_TYPE.graphics && !isMobile && 'makeGraph')
    }, [isMobile, app])

    useEffect(() => {
        ![APP_TYPE.onePageSlides, APP_TYPE.infographic].includes(app) && selectableCardTypes && selectableCardTypes[0] && setCardType(selectableCardTypes[0]);
    }, [selectableCardTypes]);

    // Handle mental_model from URL query parameter
    useEffect(() => {
        if (!mental_model || ![APP_TYPE.mindkit, APP_TYPE.mindsnap].includes(app)) return;

        // Get the list of mental models for the current app
        const mentalModelsList = Mental_Models['brainstorming']?.
            map(mm => ({
                value: mm.id,
                label: t(mm.id) // We'll use the ID as the label for comparison with the URL parameter
            }));

        if (!mentalModelsList) return;

        // Find the mental model that matches the URL parameter
        const matchedModel = mentalModelsList.find(model =>
            model.label.toLowerCase() === mental_model.toLowerCase()
        );

        if (matchedModel) {
            // If we found a match, select it
            setMentalModel({
                value: matchedModel.value,
                label: matchedModel.label
            });
        } else {
            // If no match, set to "other" and fill the input field
            setMentalModel({
                value: 'other',
                inputValue: mental_model
            });
        }
    }, [mental_model, app, t]);

    useEffect(() => {
        if (!personalInfo?.birthdate || app !== APP_TYPE.horoscope) return;

        const birthDate = new Date(personalInfo.birthdate)

        // const fortuneData = getFortuneData(birthDate, personalInfo.gender);
        const report = horoscopeCalculator.calculateFullFortune(horoscopeInfo, birthDate.getFullYear(), birthDate.getMonth() + 1, birthDate.getDate(), personalInfo.gender)
        // const report =  horoscopeCalculator.generateFortuneReport(personalInfo.birthdate, personalInfo.gender)
        // const birthHour = 20;
        // const fortuneResult = astrologyCalculator.calculateFortune(birthDate, birthHour, personalInfo.gender);
        // const elementalResult = astrologyCalculator.calculateElementalData(
        //     birthDate.getFullYear(),
        //     birthDate.getMonth() + 1,
        //     birthDate.getDate(),
        //     birthHour
        // );
        // const zodiacResult = astrologyCalculator.getZodiacSign(birthDate);

        // setAstrologyInfo(report);
        setInputData({ message: JSON.stringify(report, null, 4) })
    }, [personalInfo, app, horoscopeInfo])

    const handleModeChange = (newMode) => {
        setMode(newMode);
        setCardType(null)
    };

    const handleCardTypeChange = (type) => {
        setCardType(type);
    };

    const handleInputChange = (data) => {
        setInputData(data);
    };

    const handleLanguageChange = (language) => {
        setSelectedLanguage(language);
    };

    const handleImageStyleChange = (style) => {
        setSelectedImageStyle(style);
    };

    const handleExampleSelect = (type, message) => {
        setCardType(selectableCardTypes.find(t => t.value === type));
        setInputData({ ...inputData, message });
    };

    // AI Action 处理函数
    const handleAIAction = async (action, artifact, userInput = '') => {
        if (!artifact) return;

        try {
            let aiContent = `[Given codes]:\n\n\`\`\`${artifact.type}\n${artifact.content}\n\`\`\``;

            if (userInput) {
                aiContent = aiContent + '\n\n[User input]: ' + userInput;
            }

            const app = action;

            if(action === 'fix_codes_bug' && ['svg', 'mermaid'].includes(artifact.type)) {
                action = action + '_' + artifact.type;
            }

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 180000);

            const response = await fetch(`${apiUrl}/ai/aiAction`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    data: {
                        app,
                        action,
                        mode,
                        artifactId: artifact._id,
                        type: artifact.type,
                        // lang: selectedLanguage,
                        service: 'flow',
                        objType: 'codes',
                        content: aiContent,
                    }
                }),
                credentials: 'include',
                signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const result = await response.json();

                if (result?.data) {
                    setAiGenerated(result.data);
                    toast({
                        title: t('ai_action_success'),
                        status: "success",
                        duration: 3000,
                        isClosable: true,
                    });
                }
            } else {
                throw new Error('AI action failed');
            }
        } catch (error) {
            console.error('AI action error:', error);
            toast({
                title: t('ai_action_error'),
                status: "error",
                duration: 3000,
                isClosable: true,
            });
            throw error; // 重新抛出错误，让 ArtifactCard 能够处理
        }
    };

    const handleSubmit = async (e) => {
        // e.preventDefault();
        if (!isLoggedIn) {
            onLoginRequired();
            return;
        }

        if (app === APP_TYPE.graphics && !cardType) {
            toast({
                title: t('please_select_card_type'),
                status: "warning",
                duration: 3000,
                isClosable: true,
            });
            return;
        }

        if (['video', 'link'].includes(mode) && !inputData?.url) {
            toast({
                title: t('please_enter_valid_url'),
                status: "warning",
                duration: 3000,
                isClosable: true,
            });
            return;
        }

        if (['imageInsights', 'image'].includes(mode)) {
            if (!inputData?.imageUrl) {
                toast({
                    title: t('please_upload_or_provide_image_url'),
                    status: "warning",
                    duration: 3000,
                    isClosable: true,
                });
                return;
            }
        } else if (!inputData?.message?.trim() && !inputData?.url) {
            toast({
                title: t('please_enter_valid_message'),
                status: "warning",
                duration: 3000,
                isClosable: true,
            });
            return;
        }


        if (!selectedLanguage) {
            toast({
                title: t('please_select_language'),
                status: "warning",
                duration: 3000,
                isClosable: true,
            });
            return;
        }

        let content = inputData.message;
        let videoId;
        let urlContent;
        // console.log('input data................', mode, inputData)

        setIsLoading(true);
        if (inputData?.url) {
            let url = inputData.url;
            if (mode === 'video') {
                videoId = url.includes('youtu.be/') && url.split('youtu.be/')[1].split('?')[0]
                    || url.includes('youtube.com/') && (new URL(url).searchParams.get('v') || url.split('embed/')[1]?.split('?')[0]);

                if (!videoId) {
                    toast({
                        title: t('please_enter_valid_video_url'),
                        status: "warning",
                        duration: 3000,
                        isClosable: true,
                    });
                    return;
                }
            } else {
                urlContent = await fetchUrlContent(url);

                if (urlContent.error || !urlContent?.content || typeof urlContent?.content !== 'string' || urlContent.content.trim().length < 20) {
                    toast({
                        title: urlContent.error || t('url_content_fetch_failed'),
                        status: "error",
                        duration: 3000,
                        isClosable: true,
                    });
                    setIsLoading(false);
                    return;
                }

                content = `[Title] ${urlContent.title}\n\n[Content] ${urlContent.content}`;
            }

        }

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 180000);

            const mentalModelName = mentalModel?.value === 'other' && mentalModel?.inputValue || !['other', 'auto'].includes(mentalModel?.value) && mentalModel?.label || '';

            const endpoint = `/ai/${(mode === 'imageInsights' || [APP_TYPE.graphics, APP_TYPE.onePageSlides, APP_TYPE.infographic, APP_TYPE.mindsnap, APP_TYPE.insightcards].includes(app)) && 'generateWiseCard' || 'aiAction'}`;
            let data = (mode === 'imageInsights' || [APP_TYPE.graphics, APP_TYPE.onePageSlides, APP_TYPE.infographic, APP_TYPE.mindsnap, APP_TYPE.insightcards].includes(app)) && {
                promptId: app === APP_TYPE.onePageSlides && onePageSlidesPromptId
                    || app === APP_TYPE.infographic && infographicPromptId
                    || app === APP_TYPE.mindsnap && 'infographics_mental_models'
                    || cardType.value,
                userInput: content,
                mentalModel: mentalModelName,
                imageUrl: inputData?.imageUrl,
                lang: selectedLanguage,
                mode
            } || {
                app,
                mode,
                lang: selectedLanguage,
                service: 'flow',
                objType: ['book', 'movie'].includes(mode) && 'flow_background' || 'flow',
                content: videoId && mode === 'video' ? undefined : content,
                userInput: inputData.message,
                imageUrl: inputData?.imageUrl,
                imageStyle: selectedImageStyle,
                videoId,
                mentalModel: mentalModelName,
                horoscopeInfo
            };


            const response = await fetch(`${apiUrl}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    data
                }),
                credentials: 'include',
                signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const result = await response.json();

                if (result?.data) {
                    let data = result.data;
                    if (mode === 'link' && urlContent) {
                        data.context = urlContent
                    } else if (mode === 'others') {
                        data.context = { userInput: content, title: 'Given text' }
                    }
                    setAiGenerated(data);

                    toast({
                        title: data.err || t('card_generation_success'),
                        status: data.err && 'error' || "success",
                        duration: 3000,
                        isClosable: true,
                    });
                    !data.err && app !== APP_TYPE.horoscope && setInputData({
                        ...inputData,
                        message: '',
                        // imageUrl: '',
                        url: ''
                    });
                } else {
                    const code = result?.code;
                    if (['exceed_free_trial_quota', 'exceed_daily_quota', 'exceed_msg_limit'].includes(code)) {
                        toast({
                            status: "info",
                            duration: null,
                            isClosable: true,
                            render: () => (
                                <Flex
                                    flexDirection="column"
                                    alignItems="center"
                                    rowGap={2}
                                    padding={4}
                                    pt={8}
                                    backgroundColor="blue.500"
                                    color='white'
                                    borderRadius={8}
                                    position='relative'
                                    width='500px'
                                >
                                    <Text>{code === 'exceed_free_trial_quota' ? t('free_trial_exceeded') : t('daily_quota_exceeded')}</Text>
                                    <Button onClick={() => window.open(process.env.PRICING_URL, '_blank')} size="sm" colorScheme='green'>{t('upgrade')}</Button>
                                    <div
                                        style={{ position: 'absolute', right: 8, top: 3, cursor: 'pointer' }}
                                        onClick={() => toast.closeAll()}
                                    >
                                        <CloseIcon width={3} height={3} />
                                    </div>
                                </Flex>
                            )
                        });
                    } else {
                        throw new Error('Failed to generate card');
                    }
                }
            } else if (response.status === 403) {
                onLoginRequired();
            } else {
                throw new Error('Failed to generate card');
            }
        } catch (error) {
            console.error('Error generating card:', error);
            toast({
                title: t('card_generation_error'),
                description: error.message,
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    const tabs = useMemo(() => app === APP_TYPE.mindmap && [
        { id: 'book', labelId: 'book' },
        { id: 'movie', labelId: 'movie' },
        { id: 'video', labelId: 'video' },
        { id: 'link', labelId: 'link' },
        { id: 'others', labelId: 'others' },
    ] || app === APP_TYPE.slides && [
        { id: 'topic', labelId: 'topic' },
        { id: 'link', labelId: 'link' },
        // { id: 'doc', labelId: 'doc' },
        { id: 'others', labelId: 'others' },
    ] || app === APP_TYPE.art && [
        { id: 'image', labelId: 'image' },
        { id: 'art', labelId: 'art' }
    ] || app === APP_TYPE.graphics && (isMobile ? [
        // { id: 'imageInsights', labelId: 'image_insights' },
        { id: 'makeGraph', labelId: 'make_graph' },
        { id: 'cardGuru', labelId: 'card_guru' },
        { id: 'graphChat', labelId: 'graph_chat' }
    ] : [
        { id: 'makeGraph', labelId: 'make_graph' },
        { id: 'graphChat', labelId: 'graph_chat' },
        { id: 'cardGuru', labelId: 'card_guru' },
        // { id: 'imageInsights', labelId: 'image_insights' }
    ]), [app, isMobile])


    return (
        <Box spacing={isMobile ? 3 : 6} maxWidth="900px" width="100%" align="center" pt={2}>
            <VStack spacing={isMobile ? 3 : 6} backgroundColor="#f8f8f8" p={3} borderRadius={8} pb={isMobile ? 3 : 5}>
                {
                    !!tabs?.length &&
                    <Tabs
                        variant="soft-rounded"
                        colorScheme="blue"
                        width="100%"
                        defaultIndex={0}
                        onChange={(index) => {
                            handleModeChange(tabs[index].id);
                            setInputFocus(Math.random())
                        }}
                        index={tabs.findIndex(tab => tab.id === mode) || 0}
                    >
                        <TabList
                            width="100%"
                            overflowX="auto"
                            overflowY="hidden"
                            css={{
                                '&::-webkit-scrollbar': {
                                    display: 'none'
                                },
                                'scrollbarWidth': 'none',
                                '-ms-overflow-style': 'none',
                                'whiteSpace': 'nowrap'
                            }}
                        >
                            {
                                tabs.map(tab => (
                                    <Tab key={tab.id} minWidth={120} flex={isMobile ? 'none' : 1}>{t(tab.labelId)}</Tab>
                                ))
                            }
                        </TabList>
                    </Tabs>
                }

                {
                    app !== APP_TYPE.horoscope &&
                    <VStack width="100%" spacing={2}>
                        <InputForm
                            cardType={cardType}
                            onInputChange={handleInputChange}
                            onLoginRequired={onLoginRequired}
                            onConfirm={(e) => {
                                handleSubmit(e)
                            }}
                            focus={inputFocus}
                            value={inputData.message}
                            imageUrl={inputData.imageUrl}
                            mode={mode}
                            app={app}
                        />
                        {showExample && (
                            <ExampleButtons onSelectExample={handleExampleSelect} mode={mode} />
                        )}
                    </VStack>
                }

                {
                    app === APP_TYPE.horoscope &&
                    <PersonalForm value={personalInfo} onChange={setPersonalInfo} />
                }


                <div style={{
                    width: '100%',
                    display: 'flex',
                    flexDirection: isMobile ? 'column' : 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    gap: 12
                }}>
                    {mode === 'makeGraph' && ![APP_TYPE.onePageSlides, APP_TYPE.infographic].includes(app) && (
                        <CardTypeSelector onSelect={handleCardTypeChange} value={cardType?.value} cardTypes={selectableCardTypes} mode={mode} />
                    )}
                    {mode === 'cardGuru' && (
                        <CardTypeSelector onSelect={handleCardTypeChange} value={cardType?.value} cardTypes={selectableCardTypes} mode={mode} />
                    )}
                    {[APP_TYPE.brainstorming, APP_TYPE.decision, APP_TYPE.mindkit, APP_TYPE.mindsnap, APP_TYPE.lessonPlanner].includes(app) &&
                        <MentalModelSelector app={app} value={mentalModel} onSelect={(value) => setMentalModel(value)} onInput={(event) => {
                            setMentalModel({
                                ...mentalModel,
                                inputValue: event.target.value
                            })
                        }} />
                    }
                    {
                        app === APP_TYPE.horoscope && <HoroscopeRange value={horoscopeInfo?.range}
                            onChange={(range) => setHoroscopeInfo(prev => {
                                return { ...prev, range }
                            })}
                        />
                    }
                    {
                        ![APP_TYPE.erase, APP_TYPE.avatar].includes(app) &&
                        <LanguageSelector onSelect={handleLanguageChange} value={selectedLanguage} />
                    }
                    {
                        [APP_TYPE.avatar].includes(app) &&
                        <ImageStyleSelector onSelect={handleImageStyleChange} value={selectedImageStyle} />
                    }
                    {
                        [APP_TYPE.erase].includes(app) &&
                        <div />
                    }

                    <Button
                        colorScheme="purple"
                        onClick={handleSubmit}
                        isLoading={isLoading}
                        loadingText={''}
                        paddingX={8}
                    >
                        {t(app === APP_TYPE.erase && 'erase_watermark' || 'generate')}
                    </Button>
                </div>
            </VStack>
            {
                aiGenerated &&
                <Box width={app === APP_TYPE.graphics && "fit-content" || '100%'} maxWidth="900px" mt={8}>
                    <ArtifactCard
                        data={aiGenerated}
                        setData={setAiGenerated}
                        app={app}
                        mode={mode}
                        onAIAction={handleAIAction}
                    />
                </Box>
            }
        </Box>
    );
};

export default CardGenerator;
